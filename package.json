{"name": "wow", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android --mode=DevDebug", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "permissions": "react-native setup-ios-permissions", "postinstall": "patch-package"}, "dependencies": {"@eabdullazyanov/react-native-sms-user-consent": "1.2.0", "@gorhom/bottom-sheet": "4", "@mapbox/polyline": "0.2.0", "@notifee/react-native": "7.2.0", "@react-native-camera-roll/camera-roll": "7.10.1", "@react-native-clipboard/clipboard": "1.16.3", "@react-native-community/async-storage": "1.10.0", "@react-native-community/geolocation": "2.0.2", "@react-native-community/image-editor": "4.3.0", "@react-native-community/masked-view": "0.1.10", "@react-native-community/netinfo": "11.3.2", "@react-native-community/push-notification-ios": "1.8.0", "@react-native-community/slider": "4.5.7", "@react-native-cookies/cookies": "6.2.1", "@react-native-documents/picker": "10.1.2", "@react-native-vector-icons/entypo": "12.2.0", "@react-native-vector-icons/fontello": "12.1.0", "@react-native-vector-icons/fontisto": "12.2.0", "@react-native-vector-icons/icomoon": "12.1.0", "@react-native-vector-icons/ionicons": "12.2.0", "@react-native-voice/voice": "^3.2.4", "@react-navigation/bottom-tabs": "^7.x", "@react-navigation/elements": "^2.2.4", "@react-navigation/native": "^7.x", "@react-navigation/stack": "^7.x", "@simform_solutions/react-native-audio-waveform": "2.1.5", "@twotalltotems/react-native-otp-input": "https://github.com/LightKnight3r/react-native-otp-input.git", "async": "3.2.0", "axios": "^1.7.9", "babel-plugin-transform-remove-console": "6.9.4", "d3-shape": "^3.2.0", "deprecated-react-native-prop-types": "5.0.0", "geolib": "3.3.1", "jsoncompress": "git+https://github.com/tuanhm93/node-jsoncompress.git", "lodash": "4.17.15", "lottie-ios": "4.5.0", "lottie-react-native": "7.2.4", "markdown-it": "^14.1.0", "markdown-it-math": "^4.1.1", "markdown-it-mathjax3": "^4.3.2", "moment": "2.25.3", "ms": "2.1.2", "native-base": "2.15.2", "patch-package": "8.0.0", "postinstall-postinstall": "2.1.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-animatable": "1.4.0", "react-native-background-timer": "^2.4.1", "react-native-blob-util": "0.22.2", "react-native-biometrics": "^3.0.1", "react-native-bootsplash": "6.3.3", "react-native-calendars": "1.1313.0", "react-native-callkeep": "^4.3.12", "react-native-camera": "4.2.1", "react-native-code-push": "8.3.1", "react-native-collapsible": "1.6.2", "react-native-date-picker": "5.0.2", "react-native-device-info": "10.3.0", "react-native-draggable-flatlist": "4.0.1", "react-native-easy-toast": "2.3.0", "react-native-fs": "2.20.0", "react-native-geolocation-service": "5.3.1", "react-native-gesture-handler": "2.27.2", "react-native-htmlview": "0.16.0", "react-native-image-crop-picker": "0.50.1", "react-native-image-picker": "7.0.3", "react-native-image-zoom-viewer": "3.0.1", "react-native-keychain": "^10.0.0", "react-native-linear-gradient": "2.8.3", "react-native-lottie-splash-screen": "1.0.1", "react-native-maps": "1.14.0", "react-native-markdown-display": "^7.0.2", "react-native-marked": "6.0.6", "react-native-masked-text": "1.7.2", "react-native-mathjax-svg": "https://github.com/ducduong0827/react-native-mathjax-svg", "react-native-notifier": "2.0.0", "react-native-pager-view": "^6.6.1", "react-native-parallax-scroll-view": "^0.21.3", "react-native-parsed-text": "git+https://github.com/nbtno1/react-native-parsed-text.git", "react-native-pdf": "6.7.7", "react-native-permissions": "3.10.0", "react-native-picker-select": "6.6.0", "react-native-qrcode-scanner": "1.5.4", "react-native-qrcode-svg": "^6.1.2", "react-native-reanimated": "3.19.0", "react-native-render-html": "6.3.4", "react-native-router-flux": "https://github.com/LightKnight3r/react-native-router-flux", "react-native-safe-area-context": "5.5.2", "react-native-screens": "4.13.1", "react-native-scrollable-tab-view": "https://github.com/ducduong0827/react-native-scrollable-tab-view", "react-native-share": "12.1.0", "react-native-simple-radio-button": "2.7.4", "react-native-snap-carousel": "4.0.0-beta.6", "react-native-spinkit": "1.5.0", "react-native-star-rating": "1.1.0", "react-native-status-bar-height": "https://github.com/sonnt612/react-native-status-bar-height", "react-native-step-indicator": "0.0.9", "react-native-svg": "15.12.1", "react-native-swiper": "1.6.0", "react-native-swiper-flatlist": "^3.0.15", "react-native-tab-view": "4.1.3", "react-native-text-ticker": "1.3.0", "react-native-textinput-effects": "git+https://github.com/sonnt612/react-native-textinput-effects.git", "react-native-timeline-listview": "git+https://github.com/sonnt612/react-native-timeline-listview.git", "react-native-tips": "0.0.11", "react-native-track-player": "5.0.0-alpha0", "react-native-typewriter": "^0.7.0", "react-native-video": "^6.16.1", "react-native-view-shot": "4.0.3", "react-native-walkthrough-tooltip": "1.6.0", "react-native-webrtc": "1.106.1", "react-native-webview": "13.15.0", "react-redux": "9.2.0", "redux": "3.7.2", "redux-persist": "4.10.2", "redux-thunk": "2.3.0", "rn-placeholder": "git+https://github.com/sanship2017/rn-placeholder.git#custom", "socket.io-client": "4.7.2", "text-encoding": "^0.7.0", "validator": "13.7.0"}, "resolutions": {"native-base/react-native-vector-icons": "10.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-export-namespace-from": "7.18.9", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.5", "@react-native/eslint-config": "0.79.5", "@react-native/metro-config": "0.79.5", "@react-native/typescript-config": "0.79.5", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "rnpm": {"assets": ["./assets/fonts"]}}